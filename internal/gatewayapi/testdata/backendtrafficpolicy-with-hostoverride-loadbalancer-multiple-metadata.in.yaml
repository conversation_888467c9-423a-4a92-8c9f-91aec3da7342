gateways:
- apiVersion: gateway.networking.k8s.io/v1
  kind: Gateway
  metadata:
    namespace: envoy-gateway
    name: gateway-1
  spec:
    gatewayClassName: envoy-gateway-class
    listeners:
    - name: http
      protocol: HTTP
      port: 80
      allowedRoutes:
        namespaces:
          from: All
httpRoutes:
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-1
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/header-override"
      backendRefs:
      - name: service-1
        port: 8080
- apiVersion: gateway.networking.k8s.io/v1
  kind: HTTPRoute
  metadata:
    namespace: default
    name: httproute-2
  spec:
    hostnames:
    - gateway.envoyproxy.io
    parentRefs:
    - namespace: envoy-gateway
      name: gateway-1
      sectionName: http
    rules:
    - matches:
      - path:
          value: "/metadata-override"
      backendRefs:
      - name: service-2
        port: 8080
backendTrafficPolicies:
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    namespace: default
    name: policy-for-header-override
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-1
    loadBalancer:
      type: HostOverride
      hostOverrideSettings:
        overrideHostSources:
        - metadata:
            key: "custom.host"
            path:
            - key: "service"
            - key: "host"
        fallbackPolicy: RoundRobin
- apiVersion: gateway.envoyproxy.io/v1alpha1
  kind: BackendTrafficPolicy
  metadata:
    namespace: default
    name: policy-for-metadata-override
  spec:
    targetRef:
      group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: httproute-2
    loadBalancer:
      type: HostOverride
      hostOverrideSettings:
        overrideHostSources:
        - metadata:
            key: "custom.host"
            path:
            - key: "service"
            - key: "host"
        fallbackPolicy: LeastRequest
services:
- apiVersion: v1
  kind: Service
  metadata:
    namespace: default
    name: service-1
  spec:
    clusterIP: *******
    ports:
    - port: 8080
      name: http
      protocol: TCP
      targetPort: 8080
- apiVersion: v1
  kind: Service
  metadata:
    namespace: default
    name: service-2
  spec:
    clusterIP: *******
    ports:
    - port: 8080
      name: http
      protocol: TCP
      targetPort: 8080
endpointSlices:
- apiVersion: discovery.k8s.io/v1
  kind: EndpointSlice
  metadata:
    name: endpointslice-service-1
    namespace: default
    labels:
      kubernetes.io/service-name: service-1
  addressType: IPv4
  ports:
  - name: http
    protocol: TCP
    port: 8080
  endpoints:
  - addresses:
    - "*******"
    conditions:
      ready: true
- apiVersion: discovery.k8s.io/v1
  kind: EndpointSlice
  metadata:
    name: endpointslice-service-2
    namespace: default
    labels:
      kubernetes.io/service-name: service-2
  addressType: IPv4
  ports:
  - name: http
    protocol: TCP
    port: 8080
  endpoints:
  - addresses:
    - "*******"
    conditions:
      ready: true
