- clusterName: first-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: first-route-dest/backend/0
- clusterName: second-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: second-route-dest/backend/0
- clusterName: third-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: third-route-dest/backend/0
- clusterName: fourth-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: fourth-route-dest/backend/0
- clusterName: fifth-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: fifth-route-dest/backend/0
- clusterName: sixth-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: sixth-route-dest/backend/0
- clusterName: seventh-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: seventh-route-dest/backend/0
- clusterName: eighth-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: eighth-route-dest/backend/0
- clusterName: ninth-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: ninth-route-dest/backend/0
- clusterName: tenth-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: tenth-route-dest/backend/0
- clusterName: eleventh-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: eleventh-route-dest/backend/0
- clusterName: twelfth-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: twelfth-route-dest/backend/0
- clusterName: thirteen-route-dest
  endpoints:
  - lbEndpoints:
    - endpoint:
        address:
          socketAddress:
            address: *******
            portValue: 50000
      loadBalancingWeight: 1
    loadBalancingWeight: 1
    locality:
      region: thirteen-route-dest/backend/0
