apiVersion: v1
kind: Service
metadata:
  name: lb-backend-hostoverride
  namespace: gateway-conformance-infra
spec:
  selector:
    app: lb-backend-hostoverride
  ports:
    - protocol: TCP
      port: 8080
      targetPort: 3000
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: lb-backend-hostoverride
  namespace: gateway-conformance-infra
  labels:
    app: lb-backend-hostoverride
spec:
  replicas: 3
  selector:
    matchLabels:
      app: lb-backend-hostoverride
  template:
    metadata:
      labels:
        app: lb-backend-hostoverride
    spec:
      containers:
        - name: backend
          image: gcr.io/k8s-staging-gateway-api/echo-basic:v20231214-v1.0.0-140-gf544a46e
          imagePullPolicy: IfNotPresent
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: SERVICE_NAME
              value: lb-backend-hostoverride
          resources:
            requests:
              cpu: 10m
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: BackendTrafficPolicy
metadata:
  name: host-override-header-lb-policy
  namespace: gateway-conformance-infra
spec:
  targetRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: host-override-header-lb-route
  loadBalancer:
    type: HostOverride
    hostOverrideSettings:
      overrideHostSources:
        - header: "x-custom-host"
      fallbackPolicy: RoundRobin
---
apiVersion: gateway.envoyproxy.io/v1alpha1
kind: BackendTrafficPolicy
metadata:
  name: host-override-metadata-lb-policy
  namespace: gateway-conformance-infra
spec:
  targetRefs:
    - group: gateway.networking.k8s.io
      kind: HTTPRoute
      name: host-override-metadata-lb-route
  loadBalancer:
    type: HostOverride
    hostOverrideSettings:
      overrideHostSources:
        - metadata:
            key: "custom.host"
            path:
              - key: "service"
              - key: "host"
        - header: "x-fallback-host"
      fallbackPolicy: LeastRequest
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: host-override-header-lb-route
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: same-namespace
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /host-override-header
      backendRefs:
        - name: lb-backend-hostoverride
          port: 8080
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: host-override-metadata-lb-route
  namespace: gateway-conformance-infra
spec:
  parentRefs:
    - name: same-namespace
  rules:
    - matches:
        - path:
            type: PathPrefix
            value: /host-override-metadata
      backendRefs:
        - name: lb-backend-hostoverride
          port: 8080
